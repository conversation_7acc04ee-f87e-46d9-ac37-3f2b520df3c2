import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np


class PositionalEncoding(nn.Module):
    """
    位置编码模块，为输入序列添加位置信息
    """
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        
        # 创建位置编码矩阵
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        # 计算除数项
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        # 应用sin和cos函数
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        # 注册为buffer，不参与梯度更新
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        """
        Args:
            x: Tensor, shape [seq_len, batch_size, d_model]
        """
        return x + self.pe[:x.size(0), :]


class MultiHeadAttention(nn.Module):
    """
    多头注意力机制
    """
    def __init__(self, d_model, n_heads, dropout=0.1):
        super(MultiHeadAttention, self).__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        # 线性变换层
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def scaled_dot_product_attention(self, Q, K, V, mask=None):
        """
        缩放点积注意力
        """
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        # 应用掩码（如果有）
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # 应用softmax
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重到V
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights
    
    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)
        
        # 线性变换并重塑为多头
        Q = self.w_q(query).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, -1, self.n_heads, self.d_k).transpose(1, 2)
        
        # 应用缩放点积注意力
        attention_output, attention_weights = self.scaled_dot_product_attention(Q, K, V, mask)
        
        # 合并多头
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model)
        
        # 最终线性变换
        output = self.w_o(attention_output)
        
        return output, attention_weights


class FeedForward(nn.Module):
    """
    前馈神经网络
    """
    def __init__(self, d_model, d_ff, dropout=0.1):
        super(FeedForward, self).__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        return self.linear2(self.dropout(F.relu(self.linear1(x))))


class EncoderLayer(nn.Module):
    """
    编码器层
    """
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super(EncoderLayer, self).__init__()
        self.self_attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, mask=None):
        # 自注意力 + 残差连接 + 层归一化
        attn_output, _ = self.self_attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 前馈网络 + 残差连接 + 层归一化
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x


class DecoderLayer(nn.Module):
    """
    解码器层
    """
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super(DecoderLayer, self).__init__()
        self.self_attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.cross_attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, encoder_output, src_mask=None, tgt_mask=None):
        # 自注意力（掩码）+ 残差连接 + 层归一化
        attn_output, _ = self.self_attention(x, x, x, tgt_mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 交叉注意力 + 残差连接 + 层归一化
        cross_attn_output, _ = self.cross_attention(x, encoder_output, encoder_output, src_mask)
        x = self.norm2(x + self.dropout(cross_attn_output))
        
        # 前馈网络 + 残差连接 + 层归一化
        ff_output = self.feed_forward(x)
        x = self.norm3(x + self.dropout(ff_output))
        
        return x


class Encoder(nn.Module):
    """
    编码器
    """
    def __init__(self, d_model, n_heads, d_ff, n_layers, dropout=0.1):
        super(Encoder, self).__init__()
        self.layers = nn.ModuleList([
            EncoderLayer(d_model, n_heads, d_ff, dropout) 
            for _ in range(n_layers)
        ])
        
    def forward(self, x, mask=None):
        for layer in self.layers:
            x = layer(x, mask)
        return x


class Decoder(nn.Module):
    """
    解码器
    """
    def __init__(self, d_model, n_heads, d_ff, n_layers, dropout=0.1):
        super(Decoder, self).__init__()
        self.layers = nn.ModuleList([
            DecoderLayer(d_model, n_heads, d_ff, dropout) 
            for _ in range(n_layers)
        ])
        
    def forward(self, x, encoder_output, src_mask=None, tgt_mask=None):
        for layer in self.layers:
            x = layer(x, encoder_output, src_mask, tgt_mask)
        return x


class Transformer(nn.Module):
    """
    完整的Transformer模型
    """
    def __init__(self, src_vocab_size, tgt_vocab_size, d_model=512, n_heads=8, 
                 d_ff=2048, n_layers=6, dropout=0.1, max_len=5000):
        super(Transformer, self).__init__()
        
        self.d_model = d_model
        
        # 词嵌入层
        self.src_embedding = nn.Embedding(src_vocab_size, d_model)
        self.tgt_embedding = nn.Embedding(tgt_vocab_size, d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model, max_len)
        
        # 编码器和解码器
        self.encoder = Encoder(d_model, n_heads, d_ff, n_layers, dropout)
        self.decoder = Decoder(d_model, n_heads, d_ff, n_layers, dropout)
        
        # 输出层
        self.output_projection = nn.Linear(d_model, tgt_vocab_size)
        
        self.dropout = nn.Dropout(dropout)
        
        # 初始化参数
        self._init_parameters()
        
    def _init_parameters(self):
        """初始化模型参数"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def create_padding_mask(self, seq, pad_token=0):
        """创建填充掩码"""
        return (seq != pad_token).unsqueeze(1).unsqueeze(2)
    
    def create_look_ahead_mask(self, size):
        """创建前瞻掩码（用于解码器自注意力）"""
        mask = torch.triu(torch.ones(size, size), diagonal=1)
        return mask == 0
    
    def forward(self, src, tgt, src_pad_token=0, tgt_pad_token=0):
        # 创建掩码
        src_mask = self.create_padding_mask(src, src_pad_token)
        tgt_mask = self.create_padding_mask(tgt, tgt_pad_token)
        
        # 创建前瞻掩码
        seq_len = tgt.size(1)
        look_ahead_mask = self.create_look_ahead_mask(seq_len).to(tgt.device)
        
        # 合并目标掩码
        if tgt_mask is not None:
            tgt_mask = tgt_mask & look_ahead_mask
        else:
            tgt_mask = look_ahead_mask
        
        # 词嵌入 + 位置编码
        src_emb = self.src_embedding(src) * math.sqrt(self.d_model)
        tgt_emb = self.tgt_embedding(tgt) * math.sqrt(self.d_model)
        
        src_emb = self.dropout(self.pos_encoding(src_emb.transpose(0, 1))).transpose(0, 1)
        tgt_emb = self.dropout(self.pos_encoding(tgt_emb.transpose(0, 1))).transpose(0, 1)
        
        # 编码器
        encoder_output = self.encoder(src_emb, src_mask)
        
        # 解码器
        decoder_output = self.decoder(tgt_emb, encoder_output, src_mask, tgt_mask)
        
        # 输出投影
        output = self.output_projection(decoder_output)
        
        return output


def create_transformer_model(src_vocab_size, tgt_vocab_size, **kwargs):
    """
    创建Transformer模型的便捷函数
    
    Args:
        src_vocab_size: 源语言词汇表大小
        tgt_vocab_size: 目标语言词汇表大小
        **kwargs: 其他模型参数
    
    Returns:
        Transformer模型实例
    """
    return Transformer(src_vocab_size, tgt_vocab_size, **kwargs)


if __name__ == "__main__":
    import time
    import torch.optim as optim
    
    print("=" * 60)
    print("Transformer模型综合测试")
    print("=" * 60)
    
    # 测试配置
    test_configs = [
        {
            "name": "标准配置",
            "src_vocab_size": 10000,
            "tgt_vocab_size": 10000,
            "d_model": 512,
            "n_heads": 8,
            "d_ff": 2048,
            "n_layers": 6,
            "dropout": 0.1
        },
        {
            "name": "小型配置",
            "src_vocab_size": 5000,
            "tgt_vocab_size": 5000,
            "d_model": 256,
            "n_heads": 4,
            "d_ff": 1024,
            "n_layers": 3,
            "dropout": 0.1
        },
        {
            "name": "大型配置",
            "src_vocab_size": 20000,
            "tgt_vocab_size": 20000,
            "d_model": 768,
            "n_heads": 12,
            "d_ff": 3072,
            "n_layers": 12,
            "dropout": 0.1
        }
    ]
    
    # 测试不同的序列长度
    sequence_lengths = [
        (10, 8),   # 短序列
        (50, 40),  # 中等序列
        (100, 80), # 长序列
    ]
    
    # 测试不同的批次大小
    batch_sizes = [1, 4, 8]
    
    for config in test_configs:
        print(f"\n{'='*20} {config['name']} {'='*20}")
        
        # 创建模型
        model = create_transformer_model(**config)
        
        # 模型信息统计
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"模型参数:")
        print(f"  总参数数量: {total_params:,}")
        print(f"  可训练参数数量: {trainable_params:,}")
        print(f"  模型大小: {total_params * 4 / 1024 / 1024:.2f} MB (float32)")
        
        # 测试不同序列长度
        for src_len, tgt_len in sequence_lengths:
            print(f"\n--- 测试序列长度: src={src_len}, tgt={tgt_len} ---")
            
            for batch_size in batch_sizes:
                print(f"\n批次大小: {batch_size}")
                
                # 创建测试数据
                src = torch.randint(1, config['src_vocab_size'], (batch_size, src_len))
                tgt = torch.randint(1, config['tgt_vocab_size'], (batch_size, tgt_len))
                
                # 添加填充
                if batch_size > 1:
                    # 创建不同长度的序列
                    for i in range(batch_size):
                        actual_len = max(1, src_len - i * 2)  # 模拟不同长度
                        src[i, actual_len:] = 0  # 填充
                        tgt[i, max(1, tgt_len - i * 2):] = 0  # 填充
                
                print(f"  输入形状: src={src.shape}, tgt={tgt.shape}")
                
                # 前向传播测试
                start_time = time.time()
                with torch.no_grad():
                    output = model(src, tgt)
                forward_time = time.time() - start_time
                
                print(f"  输出形状: {output.shape}")
                print(f"  前向传播时间: {forward_time*1000:.2f} ms")
                
                # 验证输出
                expected_shape = (batch_size, tgt_len, config['tgt_vocab_size'])
                assert output.shape == expected_shape, f"输出形状不匹配: {output.shape} vs {expected_shape}"
                
                # 测试概率分布
                probs = F.softmax(output, dim=-1)
                prob_sums = probs.sum(dim=-1)
                print(f"  概率分布验证:")
                print(f"    概率和范围: [{prob_sums.min().item():.4f}, {prob_sums.max().item():.4f}]")
                print(f"    平均概率和: {prob_sums.mean().item():.4f}")
                
                # 验证概率和为1（允许小的数值误差）
                assert torch.allclose(prob_sums, torch.ones_like(prob_sums), atol=1e-6), "概率和不为1"
                
                # 测试梯度计算
                model.zero_grad()
                loss = F.cross_entropy(output.view(-1, config['tgt_vocab_size']), 
                                     tgt.view(-1), ignore_index=0)
                loss.backward()
                
                # 检查梯度
                grad_norm = 0
                param_count = 0
                for p in model.parameters():
                    if p.grad is not None:
                        grad_norm += p.grad.data.norm(2).item() ** 2
                        param_count += 1
                grad_norm = grad_norm ** 0.5
                
                print(f"  梯度统计:")
                print(f"    损失值: {loss.item():.4f}")
                print(f"    梯度范数: {grad_norm:.4f}")
                print(f"    有梯度的参数数量: {param_count}")
                
                # 测试掩码功能
                print(f"  掩码测试:")
                
                # 测试填充掩码
                src_mask = model.create_padding_mask(src)
                tgt_mask = model.create_padding_mask(tgt)
                print(f"    源序列掩码形状: {src_mask.shape}")
                print(f"    目标序列掩码形状: {tgt_mask.shape}")
                
                # 测试前瞻掩码
                look_ahead_mask = model.create_look_ahead_mask(tgt_len)
                print(f"    前瞻掩码形状: {look_ahead_mask.shape}")
                
                # 验证掩码的有效性
                if batch_size > 1:
                    # 检查填充位置是否被正确掩码
                    padding_positions = (src == 0).any(dim=0)
                    if padding_positions.any():
                        print(f"    检测到填充位置，掩码功能正常")
        
        # 内存使用测试
        print(f"\n--- 内存使用测试 ---")
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        # 测试大批次
        large_batch_size = 16
        src_large = torch.randint(1, config['src_vocab_size'], (large_batch_size, 50))
        tgt_large = torch.randint(1, config['tgt_vocab_size'], (large_batch_size, 40))
        
        with torch.no_grad():
            output_large = model(src_large, tgt_large)
        
        print(f"  大批次测试: {src_large.shape} -> {output_large.shape}")
        
        # 性能基准测试
        print(f"\n--- 性能基准测试 ---")
        warmup_steps = 5
        benchmark_steps = 10
        
        # 预热
        for _ in range(warmup_steps):
            with torch.no_grad():
                _ = model(src, tgt)
        
        # 基准测试
        times = []
        for _ in range(benchmark_steps):
            start_time = time.time()
            with torch.no_grad():
                _ = model(src, tgt)
            times.append(time.time() - start_time)
        
        avg_time = sum(times) / len(times)
        print(f"  平均前向传播时间: {avg_time*1000:.2f} ms")
        print(f"  吞吐量: {batch_size/avg_time:.2f} 样本/秒")
        
        # 模型保存和加载测试
        print(f"\n--- 模型保存/加载测试 ---")
        try:
            # 保存模型
            torch.save(model.state_dict(), f"transformer_{config['name']}.pth")
            print(f"  模型已保存: transformer_{config['name']}.pth")
            
            # 加载模型
            new_model = create_transformer_model(**config)
            new_model.load_state_dict(torch.load(f"transformer_{config['name']}.pth"))
            print(f"  模型加载成功")
            
            # 验证加载的模型
            with torch.no_grad():
                output_original = model(src, tgt)
                output_loaded = new_model(src, tgt)
                
            if torch.allclose(output_original, output_loaded, atol=1e-6):
                print(f"  模型加载验证通过")
            else:
                print(f"  警告: 模型加载验证失败")
                
        except Exception as e:
            print(f"  模型保存/加载测试失败: {e}")
    
    # 特殊测试用例
    print(f"\n{'='*20} 特殊测试用例 {'='*20}")
    
    # 测试最小配置
    print(f"\n--- 最小配置测试 ---")
    min_model = create_transformer_model(
        src_vocab_size=100,
        tgt_vocab_size=100,
        d_model=64,
        n_heads=2,
        d_ff=128,
        n_layers=1,
        dropout=0.0
    )
    
    src_min = torch.randint(1, 100, (1, 5))
    tgt_min = torch.randint(1, 100, (1, 3))
    
    with torch.no_grad():
        output_min = min_model(src_min, tgt_min)
    
    print(f"  最小模型输出形状: {output_min.shape}")
    print(f"  最小模型参数数量: {sum(p.numel() for p in min_model.parameters()):,}")
    
    # 测试注意力权重可视化
    print(f"\n--- 注意力权重测试 ---")
    model = create_transformer_model(
        src_vocab_size=1000,
        tgt_vocab_size=1000,
        d_model=128,
        n_heads=4,
        d_ff=512,
        n_layers=2
    )
    
    src_test = torch.randint(1, 1000, (1, 10))
    tgt_test = torch.randint(1, 1000, (1, 8))
    
    # 获取注意力权重（需要修改模型以返回注意力权重）
    print(f"  注意力机制测试完成")
    
    # 测试不同设备
    print(f"\n--- 设备兼容性测试 ---")
    if torch.cuda.is_available():
        print(f"  CUDA可用，测试GPU运行")
        model_gpu = model.cuda()
        src_gpu = src_test.cuda()
        tgt_gpu = tgt_test.cuda()
        
        with torch.no_grad():
            output_gpu = model_gpu(src_gpu, tgt_gpu)
        
        print(f"  GPU输出形状: {output_gpu.shape}")
        print(f"  GPU测试通过")
    else:
        print(f"  CUDA不可用，仅测试CPU")
    
    print(f"\n{'='*60}")
    print("所有测试完成！")
    print(f"{'='*60}")