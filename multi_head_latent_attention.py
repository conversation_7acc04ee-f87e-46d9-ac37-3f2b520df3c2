import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import time
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Union
import warnings


class MultiHeadLatentAttention(nn.Module):
    """
    Multi-head Latent Attention module with improved efficiency and robustness.

    This module uses learnable latent queries to compress information from input sequences,
    reducing computational complexity from O(n²) to O(n×k) where k is the number of latents.

    Key improvements:
    - Better input validation and error handling
    - Configurable attention temperature
    - Optional residual connections
    - Layer normalization for stability
    - Gradient checkpointing support
    - Memory-efficient attention computation
    """

    def __init__(
        self,
        d_model: int,
        num_heads: int,
        num_latents: int,
        latent_dim: Optional[int] = None,
        dropout: float = 0.1,
        temperature: float = 1.0,
        use_residual: bool = True,
        use_layer_norm: bool = True,
        gradient_checkpointing: bool = False
    ):
        """
        Initialize Multi-head Latent Attention module.

        Args:
            d_model: Model dimension (must be positive)
            num_heads: Number of attention heads (must be positive and divide latent_dim)
            num_latents: Number of latent queries (must be positive)
            latent_dim: Dimension of latent space (default: d_model, must be divisible by num_heads)
            dropout: Dropout rate (0.0 to 1.0)
            temperature: Attention temperature for controlling sharpness (default: 1.0)
            use_residual: Whether to use residual connections (default: True)
            use_layer_norm: Whether to use layer normalization (default: True)
            gradient_checkpointing: Whether to use gradient checkpointing for memory efficiency

        Raises:
            ValueError: If parameters are invalid
            TypeError: If parameters have wrong types
        """
        super().__init__()

        # Input validation
        self._validate_inputs(d_model, num_heads, num_latents, latent_dim, dropout, temperature)

        self.d_model = d_model
        self.num_heads = num_heads
        self.num_latents = num_latents
        self.latent_dim = latent_dim or d_model
        self.head_dim = self.latent_dim // num_heads
        self.dropout_rate = dropout
        self.temperature = temperature
        self.use_residual = use_residual
        self.use_layer_norm = use_layer_norm
        self.gradient_checkpointing = gradient_checkpointing

        # Learnable latent queries with improved initialization
        self.latent_queries = nn.Parameter(torch.empty(num_latents, self.latent_dim))

        # Cross-attention layers (latent queries attend to input)
        self.cross_attn_q = nn.Linear(self.latent_dim, self.latent_dim, bias=False)
        self.cross_attn_k = nn.Linear(d_model, self.latent_dim, bias=False)
        self.cross_attn_v = nn.Linear(d_model, self.latent_dim, bias=False)

        # Self-attention layers (latent queries attend to each other)
        self.self_attn_q = nn.Linear(self.latent_dim, self.latent_dim, bias=False)
        self.self_attn_k = nn.Linear(self.latent_dim, self.latent_dim, bias=False)
        self.self_attn_v = nn.Linear(self.latent_dim, self.latent_dim, bias=False)

        # Output projection with optional residual connection
        self.output_proj = nn.Linear(self.latent_dim, d_model)
        if self.use_residual and d_model != self.latent_dim:
            self.residual_proj = nn.Linear(d_model, d_model)
        else:
            self.residual_proj = None

        # Layer normalization
        if self.use_layer_norm:
            self.cross_norm = nn.LayerNorm(self.latent_dim)
            self.self_norm = nn.LayerNorm(self.latent_dim)
            self.output_norm = nn.LayerNorm(d_model)

        # Dropout layers
        self.dropout = nn.Dropout(dropout)
        self.attn_dropout = nn.Dropout(dropout)

        # Attention scaling factor
        self.scale = math.sqrt(self.head_dim) * self.temperature

        self._init_weights()

    def _validate_inputs(
        self,
        d_model: int,
        num_heads: int,
        num_latents: int,
        latent_dim: Optional[int],
        dropout: float,
        temperature: float
    ) -> None:
        """Validate input parameters."""
        if not isinstance(d_model, int) or d_model <= 0:
            raise ValueError(f"d_model must be a positive integer, got {d_model}")

        if not isinstance(num_heads, int) or num_heads <= 0:
            raise ValueError(f"num_heads must be a positive integer, got {num_heads}")

        if not isinstance(num_latents, int) or num_latents <= 0:
            raise ValueError(f"num_latents must be a positive integer, got {num_latents}")

        effective_latent_dim = latent_dim or d_model
        if effective_latent_dim % num_heads != 0:
            raise ValueError(f"latent_dim ({effective_latent_dim}) must be divisible by num_heads ({num_heads})")

        if not 0.0 <= dropout <= 1.0:
            raise ValueError(f"dropout must be between 0.0 and 1.0, got {dropout}")

        if temperature <= 0:
            raise ValueError(f"temperature must be positive, got {temperature}")

        if num_heads > effective_latent_dim:
            warnings.warn(f"num_heads ({num_heads}) > latent_dim ({effective_latent_dim}) may lead to poor performance")

        if num_latents > 1000:
            warnings.warn(f"Large num_latents ({num_latents}) may impact performance")
    
    def _init_weights(self) -> None:
        """Initialize weights using improved initialization strategies."""
        # Initialize linear layers with Xavier uniform (good for tanh/sigmoid activations)
        # or Kaiming normal (good for ReLU activations)
        linear_modules = [
            self.cross_attn_q, self.cross_attn_k, self.cross_attn_v,
            self.self_attn_q, self.self_attn_k, self.self_attn_v,
            self.output_proj
        ]

        for module in linear_modules:
            nn.init.xavier_uniform_(module.weight, gain=1.0)

        # Initialize latent queries with smaller variance for stability
        nn.init.normal_(self.latent_queries, mean=0.0, std=0.02)

        # Initialize residual projection if exists
        if self.residual_proj is not None:
            nn.init.xavier_uniform_(self.residual_proj.weight, gain=1.0)

    def _create_attention_mask(
        self,
        mask: Optional[torch.Tensor],
        query_len: int,
        key_len: int,
        batch_size: int
    ) -> Optional[torch.Tensor]:
        """
        Create properly shaped attention mask.

        Args:
            mask: Input mask [batch_size, key_len] or [batch_size, query_len, key_len]
            query_len: Length of query sequence
            key_len: Length of key sequence
            batch_size: Batch size

        Returns:
            Attention mask [batch_size, query_len, key_len] or None
        """
        if mask is None:
            return None

        if mask.dim() == 2:
            # Expand [batch_size, key_len] -> [batch_size, query_len, key_len]
            if mask.size(1) == key_len:
                mask = mask.unsqueeze(1).expand(-1, query_len, -1)
            elif mask.size(1) == query_len:
                # Assume it's a query mask, expand to cover keys
                mask = mask.unsqueeze(2).expand(-1, -1, key_len)
            else:
                raise ValueError(f"Invalid mask shape: {mask.shape}")
        elif mask.dim() == 3:
            # Already correct shape [batch_size, query_len, key_len]
            if mask.shape != (batch_size, query_len, key_len):
                raise ValueError(f"Mask shape {mask.shape} doesn't match expected {(batch_size, query_len, key_len)}")
        else:
            raise ValueError(f"Mask must be 2D or 3D, got {mask.dim()}D")

        return mask.bool()
    
    def _multi_head_attention(
        self,
        q: torch.Tensor,
        k: torch.Tensor,
        v: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = True
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Improved multi-head attention computation with better numerical stability.

        Args:
            q: queries [batch_size, seq_len_q, latent_dim]
            k: keys [batch_size, seq_len_k, latent_dim]
            v: values [batch_size, seq_len_v, latent_dim]
            mask: attention mask [batch_size, seq_len_q, seq_len_k]
            return_attention: Whether to return attention weights (for memory efficiency)

        Returns:
            output: [batch_size, seq_len_q, latent_dim]
            attention_weights: [batch_size, num_heads, seq_len_q, seq_len_k] or None
        """
        batch_size, seq_len_q, _ = q.shape
        seq_len_k = k.size(1)

        # Input validation
        if k.size(0) != batch_size or v.size(0) != batch_size:
            raise ValueError("Batch sizes of q, k, v must match")
        if k.size(1) != v.size(1):
            raise ValueError("Sequence lengths of k and v must match")

        # Reshape for multi-head attention: [batch, seq, heads, head_dim]
        q = q.view(batch_size, seq_len_q, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)

        # Use scaled dot-product attention with improved numerical stability
        if hasattr(F, 'scaled_dot_product_attention') and not return_attention:
            # Use PyTorch's optimized implementation when available (PyTorch 2.0+)
            # and when we don't need attention weights
            attn_mask = None
            if mask is not None:
                attn_mask = self._create_attention_mask(mask, seq_len_q, seq_len_k, batch_size)
                attn_mask = attn_mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
                attn_mask = ~attn_mask  # Invert for PyTorch's convention

            out = F.scaled_dot_product_attention(
                q, k, v,
                attn_mask=attn_mask,
                dropout_p=self.dropout_rate if self.training else 0.0,
                scale=1.0 / self.scale
            )
            attn_weights = None
        else:
            # Manual implementation with better numerical stability
            # Compute attention scores
            scores = torch.matmul(q, k.transpose(-2, -1)) / self.scale

            # Apply mask if provided
            if mask is not None:
                processed_mask = self._create_attention_mask(mask, seq_len_q, seq_len_k, batch_size)
                processed_mask = processed_mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
                scores = scores.masked_fill(~processed_mask, float('-inf'))

            # Apply softmax with numerical stability
            attn_weights = F.softmax(scores, dim=-1)

            # Handle NaN values that might arise from all -inf in a row
            attn_weights = torch.where(torch.isnan(attn_weights), torch.zeros_like(attn_weights), attn_weights)

            # Apply dropout
            attn_weights = self.attn_dropout(attn_weights)

            # Apply attention to values
            out = torch.matmul(attn_weights, v)

            if not return_attention:
                attn_weights = None

        # Reshape back to [batch_size, seq_len_q, latent_dim]
        out = out.transpose(1, 2).contiguous().view(batch_size, seq_len_q, self.latent_dim)

        return out, attn_weights
    
    def forward(
        self,
        x: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, Dict[str, Optional[torch.Tensor]]]]:
        """
        Forward pass with improved efficiency and error handling.

        Args:
            x: Input tensor [batch_size, seq_len, d_model]
            mask: Attention mask [batch_size, seq_len] (1 for valid, 0 for masked)
            return_attention: Whether to return attention weights (default: False for efficiency)

        Returns:
            If return_attention=False:
                output: [batch_size, seq_len, d_model]
            If return_attention=True:
                (output, attention_dict): output and dict with attention weights

        Raises:
            ValueError: If input dimensions are invalid
            RuntimeError: If computation fails
        """
        # Input validation
        if not isinstance(x, torch.Tensor):
            raise TypeError(f"Input x must be a torch.Tensor, got {type(x)}")

        if x.dim() != 3:
            raise ValueError(f"Input x must be 3D [batch_size, seq_len, d_model], got {x.dim()}D")

        batch_size, seq_len, input_dim = x.shape

        if input_dim != self.d_model:
            raise ValueError(f"Input dimension {input_dim} doesn't match model dimension {self.d_model}")

        if mask is not None and mask.shape[0] != batch_size:
            raise ValueError(f"Mask batch size {mask.shape[0]} doesn't match input batch size {batch_size}")

        # Store original input for residual connection
        residual = x

        try:
            # Expand latent queries for batch - more memory efficient
            latents = self.latent_queries.unsqueeze(0).expand(batch_size, -1, -1)

            # Cross-attention: latent queries attend to input
            cross_q = self.cross_attn_q(latents)
            cross_k = self.cross_attn_k(x)
            cross_v = self.cross_attn_v(x)

            # Create cross-attention mask
            cross_mask = None
            if mask is not None:
                cross_mask = mask.unsqueeze(1).expand(-1, self.num_latents, -1)

            # Apply cross-attention with optional gradient checkpointing
            if self.gradient_checkpointing and self.training:
                latents_attended, cross_attn_weights = torch.utils.checkpoint.checkpoint(
                    self._multi_head_attention, cross_q, cross_k, cross_v, cross_mask, return_attention
                )
            else:
                latents_attended, cross_attn_weights = self._multi_head_attention(
                    cross_q, cross_k, cross_v, cross_mask, return_attention
                )

            # Apply layer normalization after cross-attention
            if self.use_layer_norm:
                latents_attended = self.cross_norm(latents_attended)

            # Self-attention: latent queries attend to each other
            self_q = self.self_attn_q(latents_attended)
            self_k = self.self_attn_k(latents_attended)
            self_v = self.self_attn_v(latents_attended)

            # Apply self-attention
            if self.gradient_checkpointing and self.training:
                latents_refined, self_attn_weights = torch.utils.checkpoint.checkpoint(
                    self._multi_head_attention, self_q, self_k, self_v, None, return_attention
                )
            else:
                latents_refined, self_attn_weights = self._multi_head_attention(
                    self_q, self_k, self_v, None, return_attention
                )

            # Apply layer normalization after self-attention
            if self.use_layer_norm:
                latents_refined = self.self_norm(latents_refined)

            # Final output projection: use attention pooling over latents
            # This is more principled than simple averaging
            output_q = latents_refined
            output_k = cross_k  # Reuse cross-attention keys for efficiency

            # Compute attention weights for pooling
            pooling_scores = torch.matmul(output_q, output_k.transpose(-2, -1)) / self.scale

            if mask is not None:
                pooling_mask = mask.unsqueeze(1).expand(-1, self.num_latents, -1)
                pooling_scores = pooling_scores.masked_fill(~pooling_mask, float('-inf'))

            pooling_weights = F.softmax(pooling_scores, dim=-1)
            pooling_weights = torch.where(torch.isnan(pooling_weights), torch.zeros_like(pooling_weights), pooling_weights)

            # Apply pooling to get final representation
            pooled_output = torch.matmul(pooling_weights, x)  # [batch_size, num_latents, d_model]

            # Aggregate latent representations (learnable weighted average)
            latent_weights = F.softmax(torch.sum(latents_refined, dim=-1), dim=-1)  # [batch_size, num_latents]
            output = torch.sum(pooled_output * latent_weights.unsqueeze(-1), dim=1, keepdim=True)  # [batch_size, 1, d_model]
            output = output.expand(-1, seq_len, -1)  # [batch_size, seq_len, d_model]

            # Final projection
            output = self.output_proj(output)
            output = self.dropout(output)

            # Apply residual connection
            if self.use_residual:
                if self.residual_proj is not None:
                    residual = self.residual_proj(residual)
                output = output + residual

            # Apply final layer normalization
            if self.use_layer_norm:
                output = self.output_norm(output)

        except Exception as e:
            raise RuntimeError(f"Forward pass failed: {str(e)}") from e

        if return_attention:
            return output, {
                'cross_attn': cross_attn_weights,
                'self_attn': self_attn_weights
            }
        else:
            return output


    def get_attention_maps(
        self,
        x: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Get attention maps for visualization.

        Args:
            x: Input tensor [batch_size, seq_len, d_model]
            mask: Attention mask [batch_size, seq_len]

        Returns:
            Dictionary containing attention maps
        """
        with torch.no_grad():
            _, attention_dict = self.forward(x, mask, return_attention=True)
            return attention_dict

    def get_model_info(self) -> Dict[str, Union[int, float, str]]:
        """Get model configuration and statistics."""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'd_model': self.d_model,
            'num_heads': self.num_heads,
            'num_latents': self.num_latents,
            'latent_dim': self.latent_dim,
            'head_dim': self.head_dim,
            'dropout_rate': self.dropout_rate,
            'temperature': self.temperature,
            'use_residual': self.use_residual,
            'use_layer_norm': self.use_layer_norm,
            'gradient_checkpointing': self.gradient_checkpointing,
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'memory_efficiency_ratio': f"{self.num_latents}/{self.d_model} = {self.num_latents/self.d_model:.3f}"
        }

    def estimate_memory_usage(self, batch_size: int, seq_len: int) -> Dict[str, str]:
        """
        Estimate memory usage for given input dimensions.

        Args:
            batch_size: Batch size
            seq_len: Sequence length

        Returns:
            Dictionary with memory estimates
        """
        # Rough estimates in MB (assuming float32)
        bytes_per_element = 4
        mb_factor = 1024 * 1024

        input_memory = batch_size * seq_len * self.d_model * bytes_per_element / mb_factor
        latent_memory = batch_size * self.num_latents * self.latent_dim * bytes_per_element / mb_factor
        attention_memory = batch_size * self.num_heads * self.num_latents * seq_len * bytes_per_element / mb_factor

        # Standard attention would be O(seq_len^2)
        standard_attention_memory = batch_size * self.num_heads * seq_len * seq_len * bytes_per_element / mb_factor

        return {
            'input_memory_mb': f"{input_memory:.2f}",
            'latent_memory_mb': f"{latent_memory:.2f}",
            'attention_memory_mb': f"{attention_memory:.2f}",
            'total_estimated_mb': f"{input_memory + latent_memory + attention_memory:.2f}",
            'standard_attention_mb': f"{standard_attention_memory:.2f}",
            'memory_savings': f"{max(0, standard_attention_memory - attention_memory):.2f} MB saved"
        }


def create_model_variants() -> Dict[str, MultiHeadLatentAttention]:
    """Create different model variants for comparison."""
    d_model = 512

    variants = {
        'efficient': MultiHeadLatentAttention(
            d_model=d_model, num_heads=8, num_latents=16,
            dropout=0.1, use_residual=True, use_layer_norm=True
        ),
        'high_capacity': MultiHeadLatentAttention(
            d_model=d_model, num_heads=16, num_latents=64,
            dropout=0.1, use_residual=True, use_layer_norm=True
        ),
        'memory_optimized': MultiHeadLatentAttention(
            d_model=d_model, num_heads=8, num_latents=8,
            dropout=0.1, gradient_checkpointing=True
        ),
        'minimal': MultiHeadLatentAttention(
            d_model=d_model, num_heads=4, num_latents=8,
            dropout=0.0, use_residual=False, use_layer_norm=False
        )
    }

    return variants


def benchmark_model(model: MultiHeadLatentAttention, batch_size: int = 2, seq_len: int = 1000, num_runs: int = 10):
    """Benchmark model performance."""
    import time

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()

    # Create sample data
    x = torch.randn(batch_size, seq_len, model.d_model, device=device)
    mask = torch.ones(batch_size, seq_len, device=device)

    # Warmup
    with torch.no_grad():
        for _ in range(3):
            _ = model(x, mask)

    # Benchmark
    torch.cuda.synchronize() if torch.cuda.is_available() else None
    start_time = time.time()

    with torch.no_grad():
        for _ in range(num_runs):
            _ = model(x, mask)

    torch.cuda.synchronize() if torch.cuda.is_available() else None
    end_time = time.time()

    avg_time = (end_time - start_time) / num_runs
    throughput = batch_size * seq_len / avg_time

    print(f"Average time per forward pass: {avg_time*1000:.2f} ms")
    print(f"Throughput: {throughput:.0f} tokens/second")
    print(f"Memory usage: {model.estimate_memory_usage(batch_size, seq_len)}")


# Example usage and comprehensive testing
if __name__ == "__main__":
    print("🚀 Multi-Head Latent Attention - Comprehensive Testing")
    print("=" * 60)

    # Test different model configurations
    variants = create_model_variants()

    for name, model in variants.items():
        print(f"\n📊 Testing {name.upper()} variant:")
        print("-" * 40)

        # Model info
        info = model.get_model_info()
        print(f"Parameters: {info['total_parameters']:,}")
        print(f"Memory efficiency: {info['memory_efficiency_ratio']}")

        # Test with different input sizes
        test_cases = [
            (2, 100),   # Small
            (1, 1000),  # Medium
            (1, 5000),  # Large
        ]

        for batch_size, seq_len in test_cases:
            try:
                print(f"\n  Testing with batch_size={batch_size}, seq_len={seq_len}")

                # Create sample input
                x = torch.randn(batch_size, seq_len, model.d_model)
                mask = torch.ones(batch_size, seq_len)

                # Test forward pass
                start_time = time.time()
                output = model(x, mask)
                forward_time = time.time() - start_time

                # Test with attention weights
                start_time = time.time()
                output_with_attn, attn_weights = model(x, mask, return_attention=True)
                attention_time = time.time() - start_time

                print(f"    ✅ Forward pass: {forward_time*1000:.2f}ms")
                print(f"    ✅ With attention: {attention_time*1000:.2f}ms")
                print(f"    📏 Output shape: {output.shape}")

                # Memory estimation
                memory_info = model.estimate_memory_usage(batch_size, seq_len)
                print(f"    💾 Estimated memory: {memory_info['total_estimated_mb']} MB")
                print(f"    💰 Memory savings: {memory_info['memory_savings']}")

            except Exception as e:
                print(f"    ❌ Failed: {str(e)}")

    print(f"\n🎯 Running detailed benchmark on efficient variant...")
    benchmark_model(variants['efficient'])

    print(f"\n✨ All tests completed!")