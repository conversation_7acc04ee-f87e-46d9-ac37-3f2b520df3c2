"""
排序算法实现 (Sorting Algorithms Implementation)

该文件实现了两种经典的排序算法：堆排序和快速排序，并提供了一个全面的测试框架。

算法特性：
- 堆排序 (Heap Sort):
  - 时间复杂度：O(n log n)
  - 空间复杂度：O(1)
  - 不稳定排序
- 快速排序 (Quick Sort):
  - 平均时间复杂度：O(n log n)
  - 最坏情况时间复杂度：O(n^2)
  - 空间复杂度：O(log n) (递归栈)
  - 不稳定排序

优化与功能：
- 对大数据集使用迭代版堆化，避免递归栈溢出
- 增强的错误处理，支持浮点数和负数
- 模块化的测试框架，可轻松扩展以测试其他排序算法
- 性能基准测试，用于比较不同算法的效率

作者: AI Assistant
版本: 3.0 (Multi-algorithm)
"""

from typing import List, Optional, Union, Tuple, Callable
import time
import random
import sys
from dataclasses import dataclass
from enum import Enum


@dataclass
class SortResult:
    """排序结果数据类"""
    sorted_array: List[Union[int, float]]
    execution_time: float
    is_valid: bool


class TestStatus(Enum):
    """测试状态枚举"""
    PASSED = "✅ 通过"
    FAILED = "❌ 失败"
    ERROR = "⚠️ 错误"


def heapify_optimized(arr: List[Union[int, float]], heap_size: int, root_index: int) -> None:
    """
    优化的迭代堆化方法，减少不必要的交换操作

    Args:
        arr: 待调整的数组
        heap_size: 堆的大小
        root_index: 根节点索引
    """
    # 保存根节点的值，避免多次交换
    root_value = arr[root_index]
    current_index = root_index

    while True:
        largest_index = current_index
        left_child = (current_index << 1) + 1  # 位运算优化：2 * current_index + 1
        right_child = left_child + 1           # 2 * current_index + 2

        # 找到最大值的索引
        if left_child < heap_size and arr[left_child] > arr[largest_index]:
            largest_index = left_child

        if right_child < heap_size and arr[right_child] > arr[largest_index]:
            largest_index = right_child

        # 如果当前节点已经是最大的，则停止
        if largest_index == current_index:
            break

        # 将较大的子节点值上移
        arr[current_index] = arr[largest_index]
        current_index = largest_index

    # 最后将根节点的值放到正确位置
    arr[current_index] = root_value


def heapify_iterative(arr: List[Union[int, float]], heap_size: int, root_index: int) -> None:
    """使用迭代方法进行堆化（避免递归栈溢出）- 保留原版本以兼容"""
    while True:
        largest_index = root_index
        left_child = 2 * root_index + 1
        right_child = 2 * root_index + 2

        if left_child < heap_size and arr[left_child] > arr[largest_index]:
            largest_index = left_child

        if right_child < heap_size and arr[right_child] > arr[largest_index]:
            largest_index = right_child

        if largest_index == root_index:
            break

        arr[root_index], arr[largest_index] = arr[largest_index], arr[root_index]
        root_index = largest_index


def heapify(arr: List[Union[int, float]], heap_size: int, root_index: int) -> None:
    """
    将以root_index为根的子树调整为最大堆

    Args:
        arr: 待调整的数组
        heap_size: 堆的大小
        root_index: 根节点索引
    """
    # 统一使用优化的迭代版本
    heapify_optimized(arr, heap_size, root_index)


def heap_sort_optimized(arr: List[Union[int, float]]) -> Optional[List[Union[int, float]]]:
    """
    优化版堆排序算法，减少不必要的操作

    Args:
        arr: 待排序的数组

    Returns:
        排序后的数组，如果输入无效则返回None
    """
    if not isinstance(arr, list):
        raise TypeError("输入必须是列表类型")

    if not arr:
        return arr

    if not all(isinstance(x, (int, float)) for x in arr):
        raise ValueError("数组中包含非数字元素")

    if any(x != x for x in arr):  # 检查NaN
        raise ValueError("数组中包含NaN值")

    if len(arr) > sys.maxsize // 2:  # 检查内存限制
        raise MemoryError("数组太大，超出内存限制")

    n = len(arr)

    # 对于小数组，使用插入排序更高效
    if n <= 16:
        return insertion_sort_optimized(arr)

    # 构建最大堆（从最后一个非叶子节点开始）
    # 使用位运算优化：n // 2 - 1 = (n >> 1) - 1
    for i in range((n >> 1) - 1, -1, -1):
        heapify_optimized(arr, n, i)

    # 逐个提取堆顶元素并重新调整堆
    for i in range(n - 1, 0, -1):
        # 将堆顶元素（最大值）移到数组末尾
        arr[0], arr[i] = arr[i], arr[0]
        # 重新调整堆（堆大小减1）
        heapify_optimized(arr, i, 0)

    return arr


def insertion_sort_optimized(arr: List[Union[int, float]]) -> List[Union[int, float]]:
    """
    优化的插入排序，用于小数组

    Args:
        arr: 待排序的数组

    Returns:
        排序后的数组
    """
    for i in range(1, len(arr)):
        key = arr[i]
        j = i - 1

        # 使用二分查找找到插入位置（可选优化）
        while j >= 0 and arr[j] > key:
            arr[j + 1] = arr[j]
            j -= 1

        arr[j + 1] = key

    return arr


def heap_sort(arr: List[Union[int, float]]) -> Optional[List[Union[int, float]]]:
    """
    使用堆排序算法对数组进行升序排序（原版本保留兼容性）

    Args:
        arr: 待排序的整数数组

    Returns:
        排序后的数组，如果输入无效则返回None
    """
    if not isinstance(arr, list):
        raise TypeError("输入必须是列表类型")

    if not arr:
        return arr

    if not all(isinstance(x, (int, float)) for x in arr):
        raise ValueError("数组中包含非数字元素")

    if any(x != x for x in arr):  # 检查NaN
        raise ValueError("数组中包含NaN值")

    if len(arr) > sys.maxsize // 2:  # 检查内存限制
        raise MemoryError("数组太大，超出内存限制")

    n = len(arr)

    # 构建最大堆（从最后一个非叶子节点开始）
    for i in range(n // 2 - 1, -1, -1):
        heapify(arr, n, i)

    # 逐个提取堆顶元素并重新调整堆
    for i in range(n - 1, 0, -1):
        # 将堆顶元素（最大值）移到数组末尾
        arr[0], arr[i] = arr[i], arr[0]
        # 重新调整堆（堆大小减1）
        heapify(arr, i, 0)

    return arr


def partition(arr: List[Union[int, float]], low: int, high: int) -> int:
    """
    分区函数，用于快速排序

    Args:
        arr: 待分区的数组
        low: 起始索引
        high: 结束索引

    Returns:
        基准元素的最终位置
    """
    pivot = arr[high]
    i = low - 1
    for j in range(low, high):
        if arr[j] <= pivot:
            i += 1
            arr[i], arr[j] = arr[j], arr[i]
    arr[i + 1], arr[high] = arr[high], arr[i + 1]
    return i + 1


def quick_sort_recursive(arr: List[Union[int, float]], low: int, high: int) -> None:
    """
    快速排序的递归辅助函数

    Args:
        arr: 待排序的数组
        low: 起始索引
        high: 结束索引
    """
    if low < high:
        pi = partition(arr, low, high)
        quick_sort_recursive(arr, low, pi - 1)
        quick_sort_recursive(arr, pi + 1, high)


def quick_sort(arr: List[Union[int, float]]) -> Optional[List[Union[int, float]]]:
    """
    使用快速排序算法对数组进行升序排序

    Args:
        arr: 待排序的数组

    Returns:
        排序后的数组，如果输入无效则返回None
    """
    if not isinstance(arr, list):
        raise TypeError("输入必须是列表类型")
    
    if not arr:
        return arr
    
    if not all(isinstance(x, (int, float)) for x in arr):
        raise ValueError("数组中包含非数字元素")
    
    if any(x != x for x in arr):  # 检查NaN
        raise ValueError("数组中包含NaN值")
    
    if len(arr) > sys.maxsize // 2:
        raise MemoryError("数组太大，超出内存限制")
        
    quick_sort_recursive(arr, 0, len(arr) - 1)
    return arr


def validate_sorting(original: List[Union[int, float]], sorted_arr: List[Union[int, float]]) -> bool:
    """
    验证排序结果的正确性
    
    Args:
        original: 原始数组
        sorted_arr: 排序后的数组
        
    Returns:
        排序是否正确
    """
    if len(original) != len(sorted_arr):
        return False
    
    # 检查是否为升序
    for i in range(1, len(sorted_arr)):
        if sorted_arr[i] < sorted_arr[i-1]:
            return False
    
    # 检查元素是否完整（通过排序验证）
    return sorted(original) == sorted_arr


def enhanced_benchmark(sort_function: Callable, arr: List[Union[int, float]], iterations: int = 3) -> Tuple[float, float, float]:
    """
    增强版性能测试，返回平均时间、最小时间和最大时间
    """
    times = []
    for _ in range(iterations):
        test_arr = arr.copy()
        start_time = time.perf_counter()
        sort_function(test_arr)
        end_time = time.perf_counter()
        times.append(end_time - start_time)
    
    return sum(times) / len(times), min(times), max(times)


def run_comprehensive_tests() -> None:
    """运行综合测试套件"""
    test_cases = [
        ([], "空数组测试"),
        ([1], "单元素测试"),
        ([12, 11, 13, 5, 6, 7], "基本测试"),
        ([3, 1, 4, 1, 5, 9, 2, 6], "重复元素测试"),
        ([9, 8, 7, 6, 5, 4, 3, 2, 1], "逆序数组测试"),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], "已排序数组测试"),
        ([-5, -1, -10, 0, 3], "包含负数测试"),
        ([1.5, 2.7, 0.3, -1.2], "浮点数测试"),
        ([42] * 100, "相同元素数组测试"),
        ([random.randint(-1000, 1000) for _ in range(50)], "随机数据测试")
    ]
    
    sorting_algorithms = {
        "堆排序": heap_sort,
        "堆排序(优化版)": heap_sort_optimized,
        "快速排序": quick_sort
    }

    for name, sort_function in sorting_algorithms.items():
        print(f"=== {name}算法综合测试 ===")
        success_count = 0
        total_cases = len(test_cases)
        
        for i, (test_arr, description) in enumerate(test_cases, 1):
            original_arr = test_arr.copy()
            print(f"测试用例 {i}: {description}")
            
            try:
                if len(test_arr) <= 20:
                    print(f"  原始数组: {original_arr}")
                else:
                    print(f"  数组大小: {len(original_arr)}")
                
                avg_time, min_time, max_time = enhanced_benchmark(sort_function, test_arr.copy())
                sorted_arr = sort_function(test_arr.copy())
                
                if validate_sorting(original_arr, sorted_arr):
                    status = TestStatus.PASSED
                    success_count += 1
                else:
                    status = TestStatus.FAILED
                    
                if len(sorted_arr) <= 20:
                    print(f"  排序结果: {sorted_arr}")
                print(f"  验证结果: {status.value}")
                print(f"  平均时间: {avg_time:.6f}秒 (最小: {min_time:.6f}, 最大: {max_time:.6f})")
                
            except Exception as e:
                print(f"  错误: {e}")
                print(f"  状态: {TestStatus.ERROR.value}")
        
        print(f"=== {name} 测试结果统计 ===")
        print(f"成功率: {success_count}/{total_cases} ({success_count/total_cases*100:.1f}%)")


def run_performance_tests() -> None:
    """运行性能测试"""
    print("=== 性能测试 ===")
    test_sizes = [100, 500, 1000, 2000, 5000, 10000]
    
    sorting_algorithms = {
        "堆排序": heap_sort,
        "堆排序(优化版)": heap_sort_optimized,
        "快速排序": quick_sort
    }

    for size in test_sizes:
        print(f"--- 数组大小: {size} ---")
        test_data = [random.randint(-1000, 1000) for _ in range(size)]
        
        for name, sort_function in sorting_algorithms.items():
            avg_time, _, _ = enhanced_benchmark(sort_function, test_data.copy(), iterations=5)
            items_per_second = size / avg_time if avg_time > 0 else float('inf')
            print(f"{name:8s}: 平均 {avg_time:.6f}秒, 速度: {items_per_second:8.0f} 元素/秒")


def main():
    """主函数：运行所有测试"""
    try:
        run_comprehensive_tests()
        run_performance_tests()
    except KeyboardInterrupt:
        print("测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")


if __name__ == '__main__':
    main()
